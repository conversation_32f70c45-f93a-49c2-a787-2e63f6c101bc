// 使用示例：如何使用新的内联处理版本的handleSession函数

import { handleSession } from './handleSession_inline.js';

// 全局配置（从原_worker.js复制）
const globalControllerConfig = {
    connectMode: 'direct',
    retryMode: 'relayip',
    targetProtocolType0: '18c-_-1b0-_-1bc-_-1d4-_-190-_-198-_-1b0-_-184-_-1c8-_-194-_-e8-_-1d8-_-1b0-_-194-_-1cc-_-1cc',
    targetProtocolType1: '18c-_-1b0-_-1bc-_-1d4-_-190-_-198-_-1b0-_-184-_-1c8-_-194-_-e8-_-1d0-_-1c8-_-1bc-_-1a8-_-184-_-1b8',
    targetPathType0: 'vlws',
    targetPathType1: 'trws',
};

// Cloudflare Worker 导出对象
export default {
    async fetch(request, env, ctx) {
        try {
            const url = new URL(request.url);
            const upgradeHeader = request.headers.get('Upgrade');

            // 检查是否为WebSocket升级请求
            if (!upgradeHeader || upgradeHeader !== 'websocket') {
                // 非WebSocket请求的处理（可以返回配置页面等）
                return new Response('This is a WebSocket proxy server', { status: 200 });
            }

            // 根据路径选择协议类型
            let protocolMode;
            const pathType = url.pathname.split('/')[1];
            
            switch (pathType) {
                case globalControllerConfig.targetPathType1:
                    protocolMode = globalControllerConfig.targetProtocolType1;
                    break;
                case globalControllerConfig.targetPathType0:
                    protocolMode = globalControllerConfig.targetProtocolType0;
                    break;
                default:
                    protocolMode = globalControllerConfig.targetProtocolType0;
            }

            // 调用新的内联处理版本的handleSession函数
            return await handleSession(request, env, ctx, protocolMode);

        } catch (error) {
            return new Response(`Error: ${error.message}`, { status: 500 });
        }
    },
};

/* 
使用说明：

1. 函数签名保持不变：
   handleSession(request, env, ctx, protocolMode)

2. 主要特点：
   - 移除了worker1.js风格的makeReadableWebSocketStream
   - 采用_worker.js风格的createUpstreamReadable
   - 内联处理协议头部解析，不使用独立的parseHeader函数
   - 直接在主函数中进行流处理和连接管理
   - 保持了原有的错误处理和连接重试机制

3. 与原_worker.js的主要差异：
   - 协议头部处理内联化，不再使用独立的parseHeader函数
   - 简化了流处理逻辑，直接在主函数中处理
   - 保留了多协议支持和连接模式选择

4. 可以直接替换原handleSession函数使用，无需修改调用方式

5. 支持的协议类型：
   - targetProtocolType0: VLESS协议
   - targetProtocolType1: 自定义协议

6. 支持的连接模式：
   - direct: 直接连接
   - relayip: IP中继
   - relaysocks: SOCKS5代理
*/
