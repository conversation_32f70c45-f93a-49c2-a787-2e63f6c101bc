import { connect } from 'cloudflare:sockets';

// 从原文件复制的全局配置
const globalControllerConfig = {
    connectMode: 'direct',
    retryMode: 'relayip',
    targetProtocolType0: '18c-_-1b0-_-1bc-_-1d4-_-190-_-198-_-1b0-_-184-_-1c8-_-194-_-e8-_-1d8-_-1b0-_-194-_-1cc-_-1cc',
    targetProtocolType1: '18c-_-1b0-_-1bc-_-1d4-_-190-_-198-_-1b0-_-184-_-1c8-_-194-_-e8-_-1d0-_-1c8-_-1bc-_-1a8-_-184-_-1b8',
    targetPathType0: 'vlws',
    targetPathType1: 'trws',
};

const globalSessionConfig = {
    connect: {
        connectMode: 'direct',
        retryMode: 'relayip',
    },
    user: {
        id: '49f37b98-c37f-4d46-be93-1fe0a742dd43',
        pass: 'a233255z',
        sha224: '419023a775279d21cdbda41971c0bb52e962f11b4f4bfba6015a268b',
    },
    relay: {
        ip: 'jp.almain126.changeip.biz',
        _port: null,
        get port() {
            if (this._port !== null) {
                return this._port;
            }
            return this.ip.includes(':') ? this.ip.split(':')[1] : (null || undefined);
        },
        set port(value) {
            this._port = value;
        },
        socks: 'web5.serv00.com:13668',
    },
};

// 辅助函数
function decodeBase64Url(encodedString) {
    return Uint8Array.from(atob(encodedString.replaceAll('-', '+').replaceAll('_', '/')), (c) => c.charCodeAt(0)).buffer;
}

function matchUuid(extractedID, uuidString) {
    uuidString = uuidString.replaceAll('-', '')
    for (let index = 0; index < 16; index++) {
        const expected = parseInt(uuidString.substring(index * 2, index * 2 + 2), 16)
        if (extractedID[index] !== expected) {
            return false
        }
    }
    return true
}

// 创建可读WebSocket流（类似worker1.js的makeReadableWebSocketStream）
function makeReadableWebSocketStream(webSocketServer, earlyDataHeader, log) {
    let readableStreamCancel = false;
    const stream = new ReadableStream({
        start(controller) {
            webSocketServer.addEventListener('message', (event) => {
                if (readableStreamCancel) {
                    return;
                }
                const message = event.data;
                controller.enqueue(message);
            });

            webSocketServer.addEventListener('close', () => {
                if (readableStreamCancel) {
                    return;
                }
                controller.close();
            });

            webSocketServer.addEventListener('error', (err) => {
                log('webSocketServer has error');
                controller.error(err);
            });

            // 处理early data
            if (earlyDataHeader) {
                try {
                    const earlyData = decodeBase64Url(earlyDataHeader);
                    controller.enqueue(earlyData);
                } catch (error) {
                    controller.error(error);
                }
            }
        },

        pull(controller) {
            // 背压处理
        },

        cancel(reason) {
            if (readableStreamCancel) {
                return;
            }
            log(`ReadableStream was canceled, due to ${reason}`)
            readableStreamCancel = true;
            if (webSocketServer && webSocketServer.close instanceof Function) {
                webSocketServer.close();
            }
        }
    });

    return stream;
}

// 内联协议头处理函数（基于原parseProtocolHeaderType0和parseProtocolHeaderType1）
function processProtocolHeader(buffer, protocolMode, wsInterface) {
    const bytes = new Uint8Array(buffer);
    const view = new DataView(buffer);
    const decoder = new TextDecoder();

    if (protocolMode === globalControllerConfig.targetProtocolType0) {
        // Type0 协议处理
        const extractedID = bytes.subarray(1, 17)
        if (!matchUuid(extractedID, globalSessionConfig.user.id)) {
            if (wsInterface && wsInterface.close instanceof Function) {
                wsInterface.close(1013, 'Invalid user');
            }
            throw new Error(`Invalid user: UUID does not match`);
        }

        const version = bytes[0];
        const optionsLength = bytes[17];
        const commandIndex = 18 + optionsLength;
        const command = bytes[commandIndex];

        const portIndex = 18 + optionsLength + 1;
        const port = view.getUint16(portIndex);

        let offset = portIndex + 2;
        const addressType = bytes[offset++];

        let hostname = ''
        switch (addressType) {
            case 1: { // IPv4
                hostname = `${bytes[offset]}.${bytes[offset + 1]}.${bytes[offset + 2]}.${bytes[offset + 3]}`;
                offset += 4;
                break;
            }
            case 2: { // Domain name
                const domainLen = bytes[offset++];
                hostname = decoder.decode(bytes.subarray(offset, offset + domainLen));
                offset += domainLen;
                break;
            }
            case 3: { // IPv6
                hostname = view.getUint16(offset).toString(16);
                for (let i = 1; i < 8; i++) {
                    hostname += ':' + view.getUint16(offset + i * 2).toString(16);
                }
                offset += 16;
                break;
            }
            case 38: { // IPv6
                for (let i = 0; i < 8; i++) {
                    hostname += (i ? ':' : '') + view.getUint16(offset + i * 2).toString(16);
                }
                offset += 16;
                break;
            }
            default: {
                throw new Error(`Unsupported address type: ${addressType}`);
            }
        }

        const rawClientData = bytes.subarray(offset);

        return {
            hasError: false,
            addressType: addressType,
            addressRemote: hostname,
            portRemote: port,
            rawDataIndex: offset,
            rawClientData
        };

    } else if (protocolMode === globalControllerConfig.targetProtocolType1) {
        // Type1 协议处理
        const crLfIndex = 56;
        const extractedPassword = decoder.decode(bytes.subarray(0, crLfIndex));
        if (extractedPassword !== globalSessionConfig.user.sha224) {
            if (wsInterface && wsInterface.close instanceof Function) {
                wsInterface.close(1013, 'Invalid password');
            }
            throw new Error(`Invalid password`)
        }

        let offset = crLfIndex + 2;
        const command = bytes[offset++];
        const addressType = bytes[offset++];

        let hostname = '';
        switch (addressType) {
            case 1: { // IPv4
                hostname = `${bytes[offset]}.${bytes[offset + 1]}.${bytes[offset + 2]}.${bytes[offset + 3]}`;
                offset += 4;
                break;
            }
            case 3: { // Domain name
                const domainLen = bytes[offset++];
                hostname = decoder.decode(bytes.subarray(offset, offset + domainLen));
                offset += domainLen;
                break;
            }
            case 4: { // IPv6
                hostname = view.getUint16(offset).toString(16);
                for (let i = 1; i < 8; i++) {
                    hostname += ':' + view.getUint16(offset + i * 2).toString(16);
                }
                offset += 16;
                break;
            }
            case 48: { // IPv6
                for (let i = 0; i < 8; i++) {
                    hostname += (i ? ':' : '') + view.getUint16(offset + i * 2).toString(16);
                }
                offset += 16;
                break;
            }
            default: {
                throw new Error(`Unsupported address type: ${addressType}`);
            }
        }

        const port = view.getUint16(offset);
        offset += 4;

        const rawClientData = bytes.subarray(offset);

        return {
            hasError: false,
            addressType: addressType,
            addressRemote: hostname,
            portRemote: port,
            rawDataIndex: offset,
            rawClientData
        };
    } else {
        return {
            hasError: true,
            message: `Unknown protocol mode: ${protocolMode}`
        };
    }
}

// SOCKS5地址解析器
function socks5AddressParser(address) {
    let [userInfo, hostInfo] = address.includes('@') ? address.split('@') : ['', address];
    let username = '', password = '';

    if (userInfo) {
        [username, password] = userInfo.includes(':') ? userInfo.split(':') : [userInfo, ''];
    }

    const [hostname, port] = hostInfo.includes(':') ?
        [hostInfo.substring(0, hostInfo.lastIndexOf(':')), hostInfo.substring(hostInfo.lastIndexOf(':') + 1)] :
        [hostInfo, '1080'];

    return { username, password, hostname, port: parseInt(port) };
}

// SOCKS5连接函数（简化版）
async function socks5Connect(addressType, addressRemote, portRemote, targetProtocol) {
    const { username, password, hostname, port } = socks5AddressParser(globalSessionConfig.relay.socks);
    let socket, reader, writer;
    const encoder = new TextEncoder()

    try {
        // Connect to the SOCKS server
        socket = await connect({ hostname, port });
        reader = socket.readable.getReader();
        writer = socket.writable.getWriter();
        if (!reader || !writer) throw new Error('Failed to get reader/writer');

        // Send SOCKS5 greeting
        const socksGreeting = new Uint8Array([5, 2, 0, 2]); // Support No Auth and Username/Password Auth
        await writer.write(socksGreeting);

        // Read server response
        let res = (await reader.read()).value;
        if (res[0] !== 0x05) throw new Error('Invalid SOCKS version');

        // Handle authentication
        if (res[1] === 0x02) { // Username/Password authentication required
            if (!username || !password) throw new Error('Authentication required but no credentials provided');

            const authRequest = new Uint8Array([1, username.length, ...encoder.encode(username), password.length, ...encoder.encode(password)]);
            await writer.write(authRequest);

            res = (await reader.read()).value;
            if (res[0] !== 0x01 || res[1] !== 0x00) throw new Error('Authentication failed');
        }

        // Build connection request
        let DSTADDR;
        switch (addressType) {
            case 1: // IPv4
                DSTADDR = new Uint8Array([1, ...addressRemote.split('.').map(Number)]);
                break;
            case 2: // Domain name
                DSTADDR = new Uint8Array([3, addressRemote.length, ...encoder.encode(addressRemote)]);
                break;
            case 3: // IPv6
                DSTADDR = new Uint8Array([4, ...addressRemote.split(':').flatMap(part => {
                    const num = parseInt(part, 16);
                    return [num >> 8, num & 0xff];
                })]);
                break;
            default:
                throw new Error(`Unsupported address type: ${addressType}`);
        }

        const socksRequest = new Uint8Array([5, 1, 0, ...DSTADDR, portRemote >> 8, portRemote & 0xff]);
        await writer.write(socksRequest);

        // Read final response
        res = (await reader.read()).value;
        if (res[1] !== 0x00) throw new Error('Connection failed');

        reader.releaseLock();
        writer.releaseLock();
        return socket;

    } catch (error) {
        if (reader) reader.releaseLock();
        if (writer) writer.releaseLock();
        if (socket) {
            try { await socket.close(); } catch (closeError) { }
        }
        throw error;
    }
}

// 创建连接函数
async function createConnection(header, mode, protocolMode) {
    const { addressType, addressRemote, portRemote } = header;
    const useTargetProtocol = protocolMode === globalControllerConfig.targetProtocolType0;

    switch (mode) {
        case 'relayip': {
            const needDirect =
                [1].includes(addressType) ||
                (useTargetProtocol && [3].includes(addressType)) ||
                (!useTargetProtocol && [4].includes(addressType));
            return needDirect
                ? connect({ hostname: addressRemote, port: portRemote })
                : connect({
                    hostname: globalSessionConfig.relay.ip,
                    port: globalSessionConfig.relay.port || portRemote,
                });
        }
        case 'relaysocks': {
            return socks5Connect(addressType, addressRemote, portRemote, useTargetProtocol);
        }
        case 'direct': {
            return connect({ hostname: addressRemote, port: portRemote });
        }
        default:
            return connect({ hostname: addressRemote, port: portRemote });
    }
}

// 单次拨号包装器
async function dial(header, mode, protocolMode) {
    const iface = await createConnection(header, mode, protocolMode);
    await iface.opened; // Return after the connection is fully established
    return iface;
}

// 主要的handleSession函数 - 内联处理版本
export async function handleSession(request, env, ctx, protocolMode) {
    const { 0: client, 1: server } = Object.values(new WebSocketPair());
    server.accept();

    const earlyHeader = request.headers.get("sec-websocket-protocol") || "";

    let address = '';
    let portWithRandomLog = '';
    const log = (info, event) => {
        console.log(`[${address}:${portWithRandomLog}] ${info}`, event || '');
    };

    // 创建可读WebSocket流
    const readableWebSocketStream = makeReadableWebSocketStream(server, earlyHeader, log);

    let remoteSocketWrapper = {
        value: null,
    };
    let isDns = false;

    // WebSocket -> Remote 数据流处理
    readableWebSocketStream.pipeTo(new WritableStream({
        async write(chunk, controller) {
            if (isDns) {
                return await handleDNSQuery(chunk, server, null, log);
            }

            if (remoteSocketWrapper.value) {
                const writer = remoteSocketWrapper.value.writable.getWriter()
                await writer.write(chunk);
                writer.releaseLock();
                return;
            }

            // 内联处理协议头部
            const headerResult = processProtocolHeader(chunk, protocolMode, server);

            if (headerResult.hasError) {
                throw new Error(headerResult.message);
            }

            const {
                addressType,
                portRemote = 443,
                addressRemote = '',
                rawDataIndex
            } = headerResult;

            address = addressRemote;
            portWithRandomLog = `${portRemote}--${Math.random()}`;

            // 检查UDP处理
            const isUDP = false; // 根据协议判断，这里简化处理
            if (isUDP) {
                if (portRemote === 53) {
                    isDns = true;
                } else {
                    throw new Error('UDP proxy only enable for DNS which is port 53');
                }
            }

            // 创建响应头
            const vlessResponseHeader = new Uint8Array([0, 0]); // 简化的响应头
            const rawClientData = chunk.slice(rawDataIndex);

            if (isDns) {
                return handleDNSQuery(rawClientData, server, vlessResponseHeader, log);
            }

            // 处理TCP出站连接
            await handleTCPOutBound(remoteSocketWrapper, addressType, addressRemote, portRemote, rawClientData, server, vlessResponseHeader, log);
        },

        close() {
            log(`readableWebSocketStream is closed`);
        },

        abort(reason) {
            log(`readableWebSocketStream aborted due to ${reason}`);
        },
    })).catch((error) => {
        log(`Stream processing error: ${error.message}`);
        server.close(1002, 'Stream processing failed');
    });

    return new Response(null, {
        status: 101,
        webSocket: client,
    });
}

// TCP出站处理函数
async function handleTCPOutBound(remoteSocket, addressType, addressRemote, portRemote, rawClientData, webSocket, vlessResponseHeader, log) {
    async function connectAndWrite(address, port, socks = false) {
        const tcpSocket = socks ? await socks5Connect(addressType, address, port, false)
            : connect({
                hostname: address,
                port: port,
            });
        remoteSocket.value = tcpSocket;
        log(`connected to ${address}:${port}`);
        const writer = tcpSocket.writable.getWriter();
        await writer.write(rawClientData);
        writer.releaseLock();
        return tcpSocket;
    }

    // 尝试连接
    try {
        await connectAndWrite(addressRemote, portRemote);
    } catch (error) {
        try {
            // 重试连接
            await connectAndWrite(addressRemote, portRemote, true); // 尝试SOCKS5
        } catch (retryError) {
            log(`Connection failed: ${retryError.message}`);
            webSocket.close(1002, 'Connection failed');
            return;
        }
    }

    // 处理远程socket到WebSocket的数据传输
    remoteSocketToWS(remoteSocket.value, webSocket, vlessResponseHeader, null, log);
}

// 远程socket到WebSocket的数据传输
async function remoteSocketToWS(remoteSocket, webSocket, vlessResponseHeader, retry, log) {
    let remoteChunkCount = 0;
    let vlessHeader = vlessResponseHeader;
    let hasIncomingData = false;

    await remoteSocket.readable
        .pipeTo(
            new WritableStream({
                start() {
                },
                async write(chunk, controller) {
                    hasIncomingData = true;
                    remoteChunkCount++;
                    if (webSocket.readyState !== 1) { // WebSocket.OPEN
                        controller.error('webSocket.readyState is not open, maybe close');
                    }
                    if (vlessHeader) {
                        webSocket.send(await new Blob([vlessHeader, chunk]).arrayBuffer());
                        vlessHeader = null;
                    } else {
                        webSocket.send(chunk);
                    }
                },
                close() {
                    log(`remoteConnection!.readable is close with hasIncomingData is ${hasIncomingData}`);
                },
                abort(reason) {
                    console.error(`remoteConnection!.readable abort`, reason);
                },
            })
        )
        .catch((error) => {
            console.error(`remoteSocketToWS has exception `, error.stack || error);
            if (webSocket && webSocket.close instanceof Function) {
                webSocket.close(1000, 'Remote connection error');
            }
        });

    if (hasIncomingData === false && retry) {
        log(`retry`);
        retry();
    }
}

// DNS查询处理函数
async function handleDNSQuery(udpChunk, webSocket, vlessResponseHeader, log) {
    try {
        const dnsServer = '*******';
        const dnsPort = 53;
        let vlessHeader = vlessResponseHeader;

        const tcpSocket = connect({
            hostname: dnsServer,
            port: dnsPort,
        });

        log(`connected to ${dnsServer}:${dnsPort}`);
        const writer = tcpSocket.writable.getWriter();
        await writer.write(udpChunk);
        writer.releaseLock();

        await tcpSocket.readable.pipeTo(new WritableStream({
            async write(chunk) {
                if (webSocket.readyState === 1) { // WebSocket.OPEN
                    if (vlessHeader) {
                        webSocket.send(await new Blob([vlessHeader, chunk]).arrayBuffer());
                        vlessHeader = null;
                    } else {
                        webSocket.send(chunk);
                    }
                }
            },
            close() {
                log(`dns server(${dnsServer}) tcp is close`);
            },
            abort(reason) {
                console.error(`dns server(${dnsServer}) tcp is abort`, reason);
            },
        }));
    } catch (error) {
        console.error(`handleDNSQuery have exception, error: ${error.message}`);
    }
}
